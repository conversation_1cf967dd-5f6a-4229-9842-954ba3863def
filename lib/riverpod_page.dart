import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'riverpod_providers.dart';

/// Riverpod Demo Page using ConsumerWidget for accessing providers
/// This is the Riverpod approach - separate providers for each piece of state
/// ConsumerWidget provides access to WidgetRef for reading providers
class RiverpodDemoPage extends ConsumerWidget {
  const RiverpodDemoPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextEditingController usernameController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Riverpod State Management'),
        backgroundColor: Colors.green.shade100,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Riverpod Demo',
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Counter Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Counter',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 10),
                      // ref.watch automatically rebuilds when provider changes
                      Consumer(
                        builder: (context, ref, child) {
                          final counter = ref.watch(counterProvider);
                          return Text(
                            '$counter',
                            style: Theme.of(context).textTheme.headlineMedium,
                          );
                        },
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              ref.read(counterProvider.notifier).decrement();
                            },
                            child: const Text('-'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              ref.read(counterProvider.notifier).increment();
                            },
                            child: const Text('+'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Username Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Username',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 10),
                      Consumer(
                        builder: (context, ref, child) {
                          final username = ref.watch(usernameProvider);
                          return Text(
                            'Hello, $username!',
                            style: Theme.of(context).textTheme.titleLarge,
                          );
                        },
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: usernameController,
                              decoration: const InputDecoration(
                                labelText: 'Enter new username',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                            onPressed: () {
                              if (usernameController.text.isNotEmpty) {
                                ref
                                    .read(usernameProvider.notifier)
                                    .update(usernameController.text);
                                usernameController.clear();
                              }
                            },
                            child: const Text('Update'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Temperature Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Temperature',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 10),
                      Consumer(
                        builder: (context, ref, child) {
                          final temperature = ref.watch(temperatureProvider);
                          return Text(
                            '${temperature.toStringAsFixed(1)}°C',
                            style: Theme.of(context).textTheme.headlineMedium,
                          );
                        },
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              ref.read(temperatureProvider.notifier).decrease();
                            },
                            child: const Text('- 0.5°C'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              ref.read(temperatureProvider.notifier).increase();
                            },
                            child: const Text('+ 0.5°C'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Reset Button
              ElevatedButton(
                onPressed: () {
                  ref.read(stateManagerProvider.notifier).resetAll();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade400,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Reset All', style: TextStyle(fontSize: 16)),
              ),

              const SizedBox(height: 20),

              // Explanation Card
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Riverpod Key Features:',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const Text('• Uses @riverpod annotation'),
                      const Text('• Separate providers for each state'),
                      const Text('• ConsumerWidget for accessing providers'),
                      const Text('• ref.watch() for reactive updates'),
                      const Text('• Code generation required'),
                      const Text('• Type-safe and compile-time checked'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
