// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'riverpod_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$counterHash() => r'ffc57f9e9ac607ca97e176e723784cb719520eb7';

/// Riverpod providers using @riverpod annotation for code generation
/// Each state variable gets its own provider - this is the Riverpod approach
/// Providers are immutable and create new state when updated
///
/// Copied from [Counter].
@ProviderFor(Counter)
final counterProvider = AutoDisposeNotifierProvider<Counter, int>.internal(
  Counter.new,
  name: r'counterProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$counterHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Counter = AutoDisposeNotifier<int>;
String _$usernameHash() => r'8dee525ae41db4ba792e2dd73250393bcd53833b';

/// See also [Username].
@ProviderFor(Username)
final usernameProvider = AutoDisposeNotifierProvider<Username, String>.internal(
  Username.new,
  name: r'usernameProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$usernameHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Username = AutoDisposeNotifier<String>;
String _$temperatureHash() => r'3894670a2bc50ead59d107301cc99be8fc44a1b5';

/// See also [Temperature].
@ProviderFor(Temperature)
final temperatureProvider =
    AutoDisposeNotifierProvider<Temperature, double>.internal(
      Temperature.new,
      name: r'temperatureProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$temperatureHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Temperature = AutoDisposeNotifier<double>;
String _$stateManagerHash() => r'ce4e6d80c2de771ec38057fca233b5d161c073c6';

/// Helper provider to reset all values at once
///
/// Copied from [StateManager].
@ProviderFor(StateManager)
final stateManagerProvider =
    AutoDisposeNotifierProvider<StateManager, void>.internal(
      StateManager.new,
      name: r'stateManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$stateManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$StateManager = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
