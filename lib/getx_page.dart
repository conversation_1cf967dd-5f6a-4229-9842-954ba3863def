import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// GetX Controller that manages all state variables using .obs (observables)
/// This is the GetX approach - all state is managed in a controller class
/// that extends GetxController and uses .obs for reactive variables
class StateController extends GetxController {
  // Observable variables - GetX automatically rebuilds UI when these change
  var counter = 0.obs;
  var username = 'Guest'.obs;
  var temperature = 20.0.obs;

  // Methods to update state
  void incrementCounter() {
    counter.value++;
  }

  void decrementCounter() {
    counter.value--;
  }

  void updateUsername(String newUsername) {
    username.value = newUsername;
  }

  void increaseTemperature() {
    temperature.value += 0.5;
  }

  void decreaseTemperature() {
    temperature.value -= 0.5;
  }

  void resetAll() {
    counter.value = 0;
    username.value = 'Guest';
    temperature.value = 20.0;
  }
}

class GetXDemoPage extends StatelessWidget {
  const GetXDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the controller - GetX will manage its lifecycle
    final StateController controller = Get.put(StateController());
    final TextEditingController usernameController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: const Text('GetX State Management'),
        backgroundColor: Colors.blue.shade100,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'GetX Demo',
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Counter Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Counter',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 10),
                      // Obx widget automatically rebuilds when observable changes
                      Obx(
                        () => Text(
                          '${controller.counter.value}',
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: controller.decrementCounter,
                            child: const Text('-'),
                          ),
                          ElevatedButton(
                            onPressed: controller.incrementCounter,
                            child: const Text('+'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Username Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Username',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 10),
                      Obx(
                        () => Text(
                          'Hello, ${controller.username.value}!',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: usernameController,
                              decoration: const InputDecoration(
                                labelText: 'Enter new username',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                            onPressed: () {
                              if (usernameController.text.isNotEmpty) {
                                controller.updateUsername(
                                  usernameController.text,
                                );
                                usernameController.clear();
                              }
                            },
                            child: const Text('Update'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Temperature Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Temperature',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 10),
                      Obx(
                        () => Text(
                          '${controller.temperature.value.toStringAsFixed(1)}°C',
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: controller.decreaseTemperature,
                            child: const Text('- 0.5°C'),
                          ),
                          ElevatedButton(
                            onPressed: controller.increaseTemperature,
                            child: const Text('+ 0.5°C'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Reset Button
              ElevatedButton(
                onPressed: controller.resetAll,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade400,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Reset All', style: TextStyle(fontSize: 16)),
              ),

              const SizedBox(height: 20),

              // Explanation Card
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'GetX Key Features:',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const Text('• Uses .obs for reactive variables'),
                      const Text('• Obx() widget for automatic rebuilds'),
                      const Text('• Get.put() for dependency injection'),
                      const Text('• Simple and lightweight approach'),
                      const Text('• No code generation required'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
