import 'package:riverpod_annotation/riverpod_annotation.dart';

// This is required for code generation
part 'riverpod_providers.g.dart';

/// Riverpod providers using @riverpod annotation for code generation
/// Each state variable gets its own provider - this is the Riverpod approach
/// Providers are immutable and create new state when updated

@riverpod
class Counter extends _$Counter {
  @override
  int build() => 0; // Initial value

  void increment() {
    state = state + 1;
  }

  void decrement() {
    state = state - 1;
  }

  void reset() {
    state = 0;
  }
}

@riverpod
class Username extends _$Username {
  @override
  String build() => 'Guest'; // Initial value

  void update(String newUsername) {
    state = newUsername;
  }

  void reset() {
    state = 'Guest';
  }
}

@riverpod
class Temperature extends _$Temperature {
  @override
  double build() => 20.0; // Initial value

  void increase() {
    state = state + 0.5;
  }

  void decrease() {
    state = state - 0.5;
  }

  void reset() {
    state = 20.0;
  }
}

/// Helper provider to reset all values at once
@riverpod
class StateManager extends _$StateManager {
  @override
  void build() {
    // This provider doesn't hold state, just provides methods
  }

  void resetAll() {
    // Access other providers and reset them
    ref.read(counterProvider.notifier).reset();
    ref.read(usernameProvider.notifier).reset();
    ref.read(temperatureProvider.notifier).reset();
  }
}
