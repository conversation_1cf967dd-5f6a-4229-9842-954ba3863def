import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:statetest/main.dart';

void main() {
  testWidgets('App navigation test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: MyApp()));

    // Verify that we start on the GetX page
    expect(find.text('GetX State Management'), findsOneWidget);

    // Tap the Riverpod tab
    await tester.tap(find.text('Riverpod Demo'));
    await tester.pump();

    // Verify that we're now on the Riverpod page
    expect(find.text('Riverpod State Management'), findsOneWidget);
  });

  testWidgets('GetX counter functionality test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: MyApp()));

    // Verify that we're on GetX page and counter starts at 0
    expect(find.text('GetX State Management'), findsOneWidget);
    expect(find.text('0'), findsAtLeastNWidgets(1));

    // Find and tap the increment button
    final incrementButtons = find.text('+');
    await tester.tap(incrementButtons.first);
    await tester.pump();

    // Verify that counter has incremented
    expect(find.text('1'), findsAtLeastNWidgets(1));
  });

  testWidgets('Riverpod counter functionality test', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: MyApp()));

    // Navigate to Riverpod page
    await tester.tap(find.text('Riverpod Demo'));
    await tester.pump();

    // Verify that we're on Riverpod page
    expect(find.text('Riverpod State Management'), findsOneWidget);
    expect(find.text('0'), findsAtLeastNWidgets(1));

    // Find and tap the increment button
    final incrementButtons = find.text('+');
    await tester.tap(incrementButtons.first);
    await tester.pump();

    // Verify that counter has incremented
    expect(find.text('1'), findsAtLeastNWidgets(1));
  });

  testWidgets('Basic app functionality test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: MyApp()));

    // Verify initial state
    expect(find.text('GetX State Management'), findsOneWidget);
    expect(find.text('Hello, Guest!'), findsAtLeastNWidgets(1));
    expect(find.text('20.0°C'), findsAtLeastNWidgets(1));
  });
}
