#!/bin/bash

# Flutter State Management Demo Setup Script
# This script sets up the project and generates the required Riverpod code

echo "🚀 Setting up Flutter State Management Demo..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    echo "Visit: https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "✅ Flutter found"

# Get dependencies
echo "📦 Installing dependencies..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"

# Generate Riverpod code
echo "🔧 Generating Riverpod code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

if [ $? -ne 0 ]; then
    echo "❌ Failed to generate Riverpod code"
    exit 1
fi

echo "✅ Riverpod code generated"

# Run tests
echo "🧪 Running tests..."
flutter test

if [ $? -ne 0 ]; then
    echo "❌ Tests failed"
    exit 1
fi

echo "✅ All tests passed"

# Analyze code
echo "🔍 Analyzing code..."
flutter analyze

if [ $? -ne 0 ]; then
    echo "❌ Code analysis failed"
    exit 1
fi

echo "✅ Code analysis passed"

echo ""
echo "🎉 Setup complete! Your Flutter State Management Demo is ready."
echo ""
echo "To run the app:"
echo "  flutter run"
echo ""
echo "To watch for changes and regenerate Riverpod code:"
echo "  flutter packages pub run build_runner watch"
echo ""
echo "Happy coding! 🚀"
