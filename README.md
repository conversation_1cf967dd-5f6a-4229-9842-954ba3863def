# Flutter State Management Demo: GetX vs Riverpod

A Flutter application demonstrating the differences between GetX and Riverpod state management approaches.

## Features

This app showcases identical functionality implemented with both GetX and Riverpod:

### State Variables (3 on each page):
- **Counter** (int) - starts at 0
- **Username** (String) - starts at "Guest"
- **Temperature** (double) - starts at 20.0

### UI Components:
- Counter with increment/decrement buttons
- Username display with text field and update button
- Temperature display with increase/decrease buttons (±0.5°C)
- Reset All button to reset all values
- Bottom navigation to switch between GetX and Riverpod demos

## Key Differences

### GetX Approach:
- Uses `GetxController` with `.obs` variables
- `Obx()` widget for automatic UI rebuilds
- `Get.put()` for dependency injection
- Simple and lightweight
- No code generation required

### Riverpod Approach:
- Uses `@riverpod` annotation with separate providers
- `ConsumerWidget` for accessing providers
- `ref.watch()` for reactive updates
- Type-safe and compile-time checked
- Requires code generation

## Setup Instructions

### Quick Setup (Recommended)
Run the setup script to automatically install dependencies, generate code, and run tests:

```bash
./setup.sh
```

### Manual Setup

#### 1. Install Dependencies
```bash
flutter pub get
```

#### 2. Generate Riverpod Code
Since Riverpod uses code generation, you need to run the build runner:

```bash
# One-time generation
flutter packages pub run build_runner build

# Or watch for changes (recommended during development)
flutter packages pub run build_runner watch
```

#### 3. Run Tests
```bash
flutter test
```

#### 4. Run the App
```bash
flutter run
```

## Dependencies Used

- **GetX**: ^4.7.2 - Simple state management
- **Riverpod**: ^2.6.1 - Advanced state management with code generation
- **build_runner**: ^2.4.13 - Code generation tool
- **riverpod_generator**: ^2.6.2 - Riverpod code generator

## Project Structure

```
lib/
├── main.dart                    # App setup and navigation
├── getx_page.dart              # GetX implementation
├── riverpod_page.dart          # Riverpod implementation
├── riverpod_providers.dart     # Riverpod providers
└── riverpod_providers.g.dart   # Generated Riverpod code (auto-generated)
test/
└── widget_test.dart            # Widget tests for both implementations
setup.sh                       # Automated setup script
```

## Build Runner Commands

```bash
# Generate code once
flutter packages pub run build_runner build

# Generate code and watch for changes
flutter packages pub run build_runner watch

# Clean generated files and regenerate
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## Notes

- Both pages have identical UI and functionality
- The app demonstrates the same state management patterns using different approaches
- GetX is simpler to set up but less type-safe
- Riverpod requires more setup but provides better compile-time safety and testing capabilities
